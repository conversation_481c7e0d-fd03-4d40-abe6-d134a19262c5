import pytest
import wa_membership_checker
from datetime import datetime

testfiles = [
  "../tests/baseline.tsv",
]

@pytest.mark.parametrize("testfile", testfiles, [])
def test_never_left(testfile):
  """
  Are all members w/o an exit after their last join marked?
  """
  ts = [datetime.now()]
  # get the scripts opinion about relevant_members AND the raw memberships f/ checking that opinion
  relevant_members, memberships = wa_membership_checker.main(testfile, ts)
  # get members without exits (rather stupidly I might add)
  control = set()
  for mem in memberships:
    if not memberships[mem].exits:
      # user never left
      control.add(mem)
    elif memberships[mem].exits[-1] == "Nicht feststellbar":
      # last leave is "Nicht feststellbar"
      control.add(mem)
    elif len(memberships[mem].exits) < len(memberships[mem].joins):
      control.add(mem)
    elif memberships[mem].exits.count("Nicht feststellbar") > 1:
      control.add(mem)
  # check if script's-answer is congruent with control-answer
  assert control == relevant_members
