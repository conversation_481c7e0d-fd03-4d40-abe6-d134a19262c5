# WhatsApp Membership Checker

A Python tool that analyzes WhatsApp group membership data to determine which users were members of a group at specific timestamps. This tool processes TSV files generated by [wa-db-parser](http://gitea.digifors.local/DigiFors/wa-db-parser) and outputs CSV files containing the WhatsApp IDs of users who were group members at the specified time(s).

## Features

- **Timestamp-based membership analysis**: Determine group membership at any specific point in time
- **Multiple timestamp support**: Process multiple timestamps from a file or single timestamp input
- **Flexible timestamp formats**: Supports both German (`dd.mm.yyyy hh:mm:ss`) and ISO (`yyyy-mm-dd hh:mm:ss`) formats
- **Membership timespan tracking**: Tracks join/leave events to calculate accurate membership periods
- **CSV output**: Generates organized CSV reports with member lists for each timestamp
- **Interactive CLI**: User-friendly command-line interface with prompts
- **Batch processing**: Support for processing multiple timestamps from a file

## Prerequisites

- Python 3.7+
- Required Python packages:
  - `cprint` (for colored terminal output)
  - `pytest` (for running tests)

## Installation

1. Clone this repository:
```bash
git clone <repository-url>
cd wa-membership-checker
```

2. Install dependencies:
```bash
pip install cprint pytest
```

## Input Format

The tool expects a TSV (Tab-Separated Values) file with the following structure:

```
Zeitpunkt	Event	Teilnehmer
04.07.2021 19:49:20	Einladungslink (20)	<EMAIL>
24.08.2021 00:23:20	Gruppe beigetreten (4)	<EMAIL>
14.06.2021 19:58:06	Gruppenmitglied entfernt (14)	<EMAIL>
```

### Supported Event Types

The tool recognizes the following German event keywords:
- **Join events**: "hinzugefügt", "beigetreten"
- **Exit events**: "verlassen"

### Special Values

- `Nicht feststellbar`: Used for timestamps or events that cannot be determined

## Usage

### Interactive Mode

Run the script without arguments for interactive mode:

```bash
python src/wa_membership_checker.py
```

The tool will prompt you for:
1. Path to the TSV file (from wa-db-parser)
2. Either a single timestamp or path to a timestamps file

### Command Line Mode

For testing or automation, you can provide arguments directly:

```bash
python src/wa_membership_checker.py <path_to_tsv> "<timestamp>"
```

Example:
```bash
python src/wa_membership_checker.py tests/baseline.tsv "07.04.2022 14:30:11"
```

### Timestamp Formats

Supported timestamp formats:
- German format: `24.12.2001 18:00:00`
- ISO format: `2001-12-24 18:00:00`

### Multiple Timestamps

Create a text file with one timestamp per line:
```
07.04.2022 14:30:11
07.04.2022 14:31:21
08.04.2022 10:00:00
```

## Output

The tool generates CSV files in the same directory as the input TSV file with the naming pattern:
```
<group_id>--<timestamp>.csv
```

Example output file: `baseline.tsv--07.04.2022 14-30-11.csv`

### Output Format

```csv
Mitglieder der Gruppe <group_id> zum Zeitpunkt <timestamp>
<EMAIL>
<EMAIL>
<EMAIL>
...
```

## Project Structure

```
wa-membership-checker/
├── src/
│   ├── wa_membership_checker.py    # Main application
│   └── test_wa_membership_checker.py # Unit tests
├── tests/
│   ├── baseline.tsv                # Test data
│   └── *.csv                      # Test output files
└── README.md
```

## Core Classes

### `TimestampList`
Custom list class that automatically converts string timestamps to datetime objects, supporting multiple formats.

### `Membership`
Dataclass representing a user's membership history with join/exit timestamps and utility methods.

### `CustomMembership`
Extended membership class that handles indeterminate timestamps by treating them as beginning/end of time.

## Testing

Run the test suite:

```bash
cd src
python -m pytest test_wa_membership_checker.py -v
```

The tests verify that users without exit events after their last join are correctly identified as current members.

## Algorithm

1. **Parse TSV**: Read membership events from the input file
2. **Build membership objects**: Create membership records for each WhatsApp ID
3. **Calculate timespans**: Determine periods when each user was a group member
4. **Filter by timestamp**: Find users whose membership timespans include the query timestamp
5. **Generate output**: Create sorted CSV files with member lists

## Error Handling

- Invalid timestamp formats trigger clear error messages
- Membership inconsistencies (too many joins vs. exits) are detected and reported
- File I/O errors are handled gracefully

## Limitations

- Currently supports German event descriptions only
- Requires TSV files from wa-db-parser specifically
- Output is limited to CSV format (XLSX support planned)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

## License

[Add your license information here]

## Related Tools

- [wa-db-parser](http://gitea.digifors.local/DigiFors/wa-db-parser): Generates the TSV input files for this tool