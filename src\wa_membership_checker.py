#!/usr/bin/env python3

import csv
from cprint import cprint
from datetime import datetime
from collections import UserList, deque
from dataclasses import dataclass, field
from pathlib import Path
from typing import Tuple, Dict, List

# for testing
from sys import argv

evt_dict = {
  "join": ["hinzugefügt", "beigetreten"],
  "exit": ["verlassen"],
}

class TimestampList(UserList):
  """
  Custom List for timestamps.
  Automatically (tries to) convert appended strings to a timestamp.
  Will throw a `ValueError`, if the conversion failed.
  """
  def __init__(self, *args: Tuple, **kwargs: Dict) -> None:
    super().__init__(*args, **kwargs)

  def append(self, item) -> None:
    assert isinstance(item, str) or isinstance(item, datetime), \
      f"\nTimestampList only takes strings or datetime.datetimes as items!\nYou gave a {type(item)}"
    try:
      ts = datetime.strptime(item, "%Y-%m-%d %H:%M:%S")
      super().append(ts)
      return
    except ValueError:
      pass
    try:
      ts = datetime.strptime(item, "%d.%m.%Y %H:%M:%S")
      super().append(ts)
    except ValueError:
      if item.lower() == "nicht feststellbar":
        super().append(item)
      else:
        raise ValueError(f"\nThe string \"{item}\" does not appear to represent a known timestamp!")
  

@dataclass
class Membership:
  waid: str
  joins: TimestampList = field(default_factory=lambda: TimestampList())
  exits: TimestampList = field(default_factory=lambda: TimestampList())
  @property
  def firstjoin(self) -> str:
    if len(self.joins) == 0:
      return "Nicht feststellbar"
    if isinstance(self.joins[0], str):
      return "Nicht feststellbar"
    _joins = [ ts for ts in self.joins if isinstance(ts, datetime) ]
    return min(_joins).strftime("%d.%m.%Y %H:%M:%S")
  @property
  def lastleave(self) -> str:
    if len(self.exits) == 0:
      return "Nicht feststellbar"
    if isinstance(self.exits[0], str):
      return "Nicht feststellbar"
    _exits = [ ts for ts in self.exits if isinstance(ts, datetime) ]
    return max(_exits).strftime("%d.%m.%Y %H:%M:%S")
  @property
  def membership_timespans(self) -> List[Tuple[datetime, datetime]]:
    """
    Return all timespans (start- and end-time), in which this user was a member of the group.
    """
    # check validity of joins/exits
    assert (diff:=len(self.joins) - len(self.exits)) < 2, \
      f"User {self.waid} {'joined' if diff > 0 else 'left'} the group {diff} times to often!"
    _joins, _exits = deque(self.joins), deque(self.exits)
    timespans = []
    while _joins or _exits:
      _join = _joins.popleft() if _joins else "Nicht feststellbar"
      _exit = _exits.popleft() if _exits else "Nicht feststellbar"
      timespans.append((_join, _exit))
    return timespans

 
@dataclass
class CustomMembership(Membership):
  """
  Same as normal Membership, except "Nicht feststellbar" in joins/exits
  is replaced by beginning and end of time respectively
  """
  @property
  def membership_timespans(self) -> List[Tuple[datetime, datetime]]:
    """
    Return all timespans (start- and end-time), in which this user was a member of the group.
    """
    # check validity of joins/exits
    assert (diff:=len(self.joins) - len(self.exits)) < 2, \
      f"User {self.waid} {'joined' if diff > 0 else 'left'} the group {diff} times to often!"
    _joins, _exits = deque(self.joins), deque(self.exits)
    timespans = []
    while _joins or _exits:
      _join = _joins.popleft() if _joins else datetime.fromtimestamp(0)
      _exit = _exits.popleft() if _exits else datetime.fromtimestamp(9999999999)
      timespans.append((_join, _exit))
    return timespans


def handle_input():
  # .tsv from wa-db-parser w/ memberships
  tsv = Path(input("Pfad der .tsv (von wa-db-parser.py):\n> ").replace("\"", ""))
  # relevant timestamp in local time
  _ts = input("Relevanter Zeitpunkt in Ortszeit (z.B. 24.12.2001 18:00:00):\n> ")
  if _ts:
    try:
      timestamps = [datetime.strptime(_ts, "%d.%m.%Y %H:%M:%S")]
    except Exception:
      cprint.fatal(f"\n[!!] The string {_ts} does not appear to have the correct format.")
      exit(0)
  # if no single ts was supplied, ask for and parse file with multiple timestamps
  else:
    _ts_file = Path(input("Pfad der timestamps.txt (1 Zeitpunkt pro Zeile):\n> ").replace("\"", ""))
    timestamps = []
    with _ts_file.open("r") as ts_file:
      while raw_ts := ts_file.readline():
        try:
          ts = datetime.strptime(raw_ts.strip(), "%d.%m.%Y %H:%M:%S")
          timestamps.append(ts)
        except Exception:
          print(f"The string {_ts} does not appear to have the correct format.")
  return tsv, timestamps


def parse_memberships(tsv):
  memberships = {}
  tsv = Path(tsv)
  with tsv.open("r") as f:
    reader = csv.reader(f, delimiter="\t")
    for line in reader:
      # ignore first line
      if line[0] == "Zeitpunkt":
        continue
      # read out line
      ts, evt, waid = line
      # check if this waID is already in dict
      if waid not in memberships.keys():
        memberships[waid] = CustomMembership(waid=waid)
      # parse event-type
      for keyphrase in evt_dict["join"]:
        if keyphrase.lower() in evt:
          memberships[waid].joins.append(ts)
      for keyphrase in evt_dict["exit"]:
        if keyphrase.lower() in evt:
          memberships[waid].exits.append(ts)
  return memberships


def find_members(memberships, ts):
  relevant_members = set()
  for membership in memberships.values():
    for timespan in membership.membership_timespans:
      #from pdb import set_trace
      #set_trace()
      _start, _end = timespan
      if _start <= ts <= _end:
        relevant_members.add(membership.waid)
  return relevant_members


def main(tsv=None, timestamps=None):
  if not tsv or not timestamps:
    tsv, timestamps = handle_input()
  tsv = Path(tsv)
  # parse .tsv into a list of Memberships
  memberships = parse_memberships(tsv)
  # check which memberships have the given timestamp within their timespan
  for ts in timestamps:
    members_at_ts = find_members(memberships, ts)
    # TODO:
    # implement some sort of template (preferably .xlsx)
    # save .csv in same folder as msgstore.db
    _groupid = tsv.name.replace("-members.tsv", "")
    _legible_date = ts.strftime('%d.%m.%Y %H-%M-%S')
    outfile = tsv.resolve().parent / f"{_groupid}--{_legible_date}.csv"
    with open(outfile, "w", newline="") as f:
      csvwriter = csv.writer(f, delimiter="\t")
      csvwriter.writerow([f"Mitglieder der Gruppe {_groupid} zum Zeitpunkt {_legible_date.replace('-', ':')}"])
      for item in sorted(list(members_at_ts)):
        csvwriter.writerow([item])
  return members_at_ts, memberships

if __name__ == "__main__":
  if len(argv) == 3: # for testing
    main(Path(argv[1]), [datetime.strptime(argv[2], "%d.%m.%Y %H:%M:%S")])
  else:
    main()
  print("Done.")
  input()
